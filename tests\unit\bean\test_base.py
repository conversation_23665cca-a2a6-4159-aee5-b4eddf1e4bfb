#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean基础接口测试套件
"""

import unittest
from typing import Any, Optional, TypeVar

from miniboot.bean.base import (
    BeanFactory,
    HierarchicalBeanFactory,
    ListableBeanFactory,
    ConfigurableBeanFactory,
    ProcessingPhase
)
from miniboot.processor.base import BeanPostProcessor
from miniboot.bean.definition import BeanDefinition
from miniboot.bean.scopes import BeanScope
from miniboot.errors import BeanDefinitionStoreError as NoSuchBeanDefinitionError

T = TypeVar("T")


# ==================== 测试用实现类 ====================

class TestBeanFactory(BeanFactory):
    """测试用Bean工厂实现"""

    def __init__(self):
        self._beans = {}
        self._definitions = {}

    def get_bean(self, name: str, required_type: Optional[type] = None) -> Any:
        if name not in self._beans:
            raise NoSuchBeanDefinitionError(f"No bean named '{name}' available")
        bean = self._beans[name]
        if required_type and not isinstance(bean, required_type):
            raise TypeError(f"Bean '{name}' is not of required type {required_type}")
        return bean

    def contains_bean(self, name: str) -> bool:
        return name in self._beans

    def is_singleton(self, name: str) -> bool:
        if name not in self._definitions:
            raise NoSuchBeanDefinitionError(f"No bean named '{name}' available")
        return self._definitions[name].scope == BeanScope.SINGLETON

    def get_type(self, name: str) -> Optional[type]:
        if name not in self._definitions:
            return None
        return self._definitions[name].bean_class

    def get_bean_names(self) -> list[str]:
        return list(self._beans.keys())

    def _register_bean(self, name: str, bean: Any, definition: BeanDefinition) -> None:
        """测试辅助方法"""
        self._beans[name] = bean
        self._definitions[name] = definition


class TestHierarchicalBeanFactory(TestBeanFactory, HierarchicalBeanFactory):
    """测试用分层Bean工厂实现"""

    def __init__(self, parent: Optional[BeanFactory] = None):
        super().__init__()
        self._parent = parent

    def get_parent_bean_factory(self) -> Optional[BeanFactory]:
        return self._parent

    def contains_local_bean(self, name: str) -> bool:
        return name in self._beans

    def contains_bean(self, name: str) -> bool:
        # 先检查本地，再检查父工厂
        if self.contains_local_bean(name):
            return True
        if self._parent:
            return self._parent.contains_bean(name)
        return False

    def get_bean(self, name: str, required_type: Optional[type] = None) -> Any:
        # 先尝试本地获取
        if self.contains_local_bean(name):
            return super().get_bean(name, required_type)
        # 再尝试父工厂
        if self._parent:
            return self._parent.get_bean(name, required_type)
        raise NoSuchBeanDefinitionError(f"No bean named '{name}' available")


class TestBeanPostProcessor(BeanPostProcessor):
    """测试用Bean后置处理器"""

    def __init__(self):
        self.pre_processed = []
        self.post_processed = []

    def _do_pre_process(self, bean: Any, bean_name: str) -> Any:
        self.pre_processed.append(bean_name)
        return bean

    def _do_post_process(self, bean: Any, bean_name: str) -> Any:
        self.post_processed.append(bean_name)
        return bean

    def get_order(self) -> int:
        """获取处理器执行顺序"""
        return 0


# ==================== 测试用Bean类 ====================

class TestService:
    """测试用服务类"""
    def __init__(self, name: str = "test"):
        self.name = name
        self.initialized = False

    def init(self) -> None:
        self.initialized = True


class DatabaseService:
    """测试用数据库服务"""
    def __init__(self):
        self.connected = False

    def connect(self) -> None:
        self.connected = True


# ==================== 基础接口测试 ====================

class ProcessingPhaseTestCase(unittest.TestCase):
    """ProcessingPhase枚举测试"""

    def test_processing_phase_values(self):
        """测试处理阶段枚举值"""
        self.assertEqual(ProcessingPhase.BEFORE_INITIALIZATION.value, "before_init")
        self.assertEqual(ProcessingPhase.AFTER_INITIALIZATION.value, "after_init")

    def test_processing_phase_members(self):
        """测试处理阶段枚举成员"""
        phases = list(ProcessingPhase)
        self.assertEqual(len(phases), 2)
        self.assertIn(ProcessingPhase.BEFORE_INITIALIZATION, phases)
        self.assertIn(ProcessingPhase.AFTER_INITIALIZATION, phases)


class BeanFactoryTestCase(unittest.TestCase):
    """BeanFactory基础接口测试"""

    def setUp(self):
        self.factory = TestBeanFactory()

        # 注册测试Bean
        test_service = TestService("test1")
        test_def = BeanDefinition("testService", TestService, BeanScope.SINGLETON)
        self.factory._register_bean("testService", test_service, test_def)

        db_service = DatabaseService()
        db_def = BeanDefinition("dbService", DatabaseService, BeanScope.PROTOTYPE)
        self.factory._register_bean("dbService", db_service, db_def)

    def test_get_bean_success(self):
        """测试成功获取Bean"""
        bean = self.factory.get_bean("testService")
        self.assertIsInstance(bean, TestService)
        self.assertEqual(bean.name, "test1")

    def test_get_bean_with_type_check(self):
        """测试带类型检查的Bean获取"""
        bean = self.factory.get_bean("testService", TestService)
        self.assertIsInstance(bean, TestService)

    def test_get_bean_type_mismatch(self):
        """测试类型不匹配的Bean获取"""
        with self.assertRaises(TypeError):
            self.factory.get_bean("testService", DatabaseService)

    def test_get_bean_not_found(self):
        """测试获取不存在的Bean"""
        with self.assertRaises(NoSuchBeanDefinitionError):
            self.factory.get_bean("nonExistentBean")

    def test_contains_bean(self):
        """测试Bean存在性检查"""
        self.assertTrue(self.factory.contains_bean("testService"))
        self.assertTrue(self.factory.contains_bean("dbService"))
        self.assertFalse(self.factory.contains_bean("nonExistentBean"))

    def test_is_singleton(self):
        """测试单例检查"""
        self.assertTrue(self.factory.is_singleton("testService"))
        self.assertFalse(self.factory.is_singleton("dbService"))

    def test_is_singleton_not_found(self):
        """测试不存在Bean的单例检查"""
        with self.assertRaises(NoSuchBeanDefinitionError):
            self.factory.is_singleton("nonExistentBean")

    def test_get_type(self):
        """测试获取Bean类型"""
        self.assertEqual(self.factory.get_type("testService"), TestService)
        self.assertEqual(self.factory.get_type("dbService"), DatabaseService)
        self.assertIsNone(self.factory.get_type("nonExistentBean"))

    def test_get_bean_names(self):
        """测试获取Bean名称列表"""
        names = self.factory.get_bean_names()
        self.assertIn("testService", names)
        self.assertIn("dbService", names)
        self.assertEqual(len(names), 2)

    def test_compatibility_methods(self):
        """测试兼容性方法"""
        # get() 方法
        bean = self.factory.get("testService")
        self.assertIsInstance(bean, TestService)

        # contains() 方法
        self.assertTrue(self.factory.contains("testService"))
        self.assertFalse(self.factory.contains("nonExistentBean"))

        # names() 方法
        names = self.factory.names()
        self.assertIn("testService", names)

        # singleton() 方法
        self.assertTrue(self.factory.singleton("testService"))
        self.assertFalse(self.factory.singleton("dbService"))

        # type() 方法
        self.assertEqual(self.factory.type("testService"), TestService)


class HierarchicalBeanFactoryTestCase(unittest.TestCase):
    """分层Bean工厂测试"""

    def setUp(self):
        # 创建父工厂
        self.parent_factory = TestBeanFactory()
        parent_service = TestService("parent")
        parent_def = BeanDefinition("parentService", TestService, BeanScope.SINGLETON)
        self.parent_factory._register_bean("parentService", parent_service, parent_def)

        # 创建子工厂
        self.child_factory = TestHierarchicalBeanFactory(self.parent_factory)
        child_service = DatabaseService()
        child_def = BeanDefinition("childService", DatabaseService, BeanScope.SINGLETON)
        self.child_factory._register_bean("childService", child_service, child_def)

    def test_get_parent_bean_factory(self):
        """测试获取父工厂"""
        self.assertIs(self.child_factory.get_parent_bean_factory(), self.parent_factory)

    def test_contains_local_bean(self):
        """测试本地Bean检查"""
        self.assertTrue(self.child_factory.contains_local_bean("childService"))
        self.assertFalse(self.child_factory.contains_local_bean("parentService"))

    def test_contains_bean_hierarchical(self):
        """测试分层Bean检查"""
        # 子工厂的Bean
        self.assertTrue(self.child_factory.contains_bean("childService"))
        # 父工厂的Bean
        self.assertTrue(self.child_factory.contains_bean("parentService"))
        # 不存在的Bean
        self.assertFalse(self.child_factory.contains_bean("nonExistentBean"))

    def test_get_bean_from_parent(self):
        """测试从父工厂获取Bean"""
        bean = self.child_factory.get_bean("parentService")
        self.assertIsInstance(bean, TestService)
        self.assertEqual(bean.name, "parent")

    def test_get_bean_from_child(self):
        """测试从子工厂获取Bean"""
        bean = self.child_factory.get_bean("childService")
        self.assertIsInstance(bean, DatabaseService)


class TestConfigurableBeanFactory(TestHierarchicalBeanFactory, ConfigurableBeanFactory):
    """测试用可配置Bean工厂实现"""

    def __init__(self, parent: Optional[BeanFactory] = None):
        super().__init__(parent)
        self._scopes: dict[str, BeanScope] = {}
        self._processors: list[Any] = []

    def set_parent_bean_factory(self, parent_bean_factory: Optional[BeanFactory]) -> None:
        self._parent = parent_bean_factory

    def register_scope(self, scope_name: str, scope: BeanScope) -> None:
        self._scopes[scope_name] = scope

    def add_bean_processor(self, bean_post_processor: Any) -> None:
        self._processors.append(bean_post_processor)

    def get_bean_processor_count(self) -> int:
        return len(self._processors)

    def destroy_singletons(self) -> None:
        # 简单实现：清空所有单例Bean
        singleton_names = [name for name, definition in self._definitions.items()
                          if definition.scope == BeanScope.SINGLETON]
        for name in singleton_names:
            if name in self._beans:
                del self._beans[name]


class TestListableBeanFactory(TestBeanFactory, ListableBeanFactory):
    """测试用可列举Bean工厂实现"""

    def get_beans_of_type(self, bean_type: type[T], include_non_singletons: bool = True, _allow_eager_init: bool = True) -> dict[str, T]:
        result = {}
        for name, definition in self._definitions.items():
            if issubclass(definition.bean_class, bean_type):
                if not include_non_singletons and definition.scope != BeanScope.SINGLETON:
                    continue
                if name in self._beans:
                    result[name] = self._beans[name]
        return result

    def get_bean_names_for_type(self, bean_type: type, include_non_singletons: bool = True, _allow_eager_init: bool = True) -> list[str]:
        result = []
        for name, definition in self._definitions.items():
            if issubclass(definition.bean_class, bean_type):
                if not include_non_singletons and definition.scope != BeanScope.SINGLETON:
                    continue
                result.append(name)
        return result


# ==================== 高级接口测试 ====================

class ConfigurableBeanFactoryTestCase(unittest.TestCase):
    """可配置Bean工厂测试"""

    def setUp(self):
        self.factory = TestConfigurableBeanFactory()

    def test_set_parent_bean_factory(self):
        """测试设置父工厂"""
        parent = TestBeanFactory()
        self.factory.set_parent_bean_factory(parent)
        self.assertIs(self.factory.get_parent_bean_factory(), parent)

    def test_register_scope(self):
        """测试注册作用域"""
        custom_scope = BeanScope.SINGLETON
        self.factory.register_scope("custom", custom_scope)
        self.assertIn("custom", self.factory._scopes)
        self.assertEqual(self.factory._scopes["custom"], custom_scope)

    def test_add_bean_processor(self):
        """测试添加Bean后置处理器"""
        processor = TestBeanPostProcessor()
        self.factory.add_bean_processor(processor)
        self.assertEqual(self.factory.get_bean_processor_count(), 1)
        self.assertIn(processor, self.factory._processors)

    def test_multiple_bean_processors(self):
        """测试多个Bean后置处理器"""
        processor1 = TestBeanPostProcessor()
        processor2 = TestBeanPostProcessor()

        self.factory.add_bean_processor(processor1)
        self.factory.add_bean_processor(processor2)

        self.assertEqual(self.factory.get_bean_processor_count(), 2)

    def test_destroy_singletons(self):
        """测试销毁单例Bean"""
        # 注册单例Bean
        singleton_service = TestService("singleton")
        singleton_def = BeanDefinition("singletonService", TestService, BeanScope.SINGLETON)
        self.factory._register_bean("singletonService", singleton_service, singleton_def)

        # 注册原型Bean
        prototype_service = TestService("prototype")
        prototype_def = BeanDefinition("prototypeService", TestService, BeanScope.PROTOTYPE)
        self.factory._register_bean("prototypeService", prototype_service, prototype_def)

        # 验证Bean存在
        self.assertTrue(self.factory.contains_bean("singletonService"))
        self.assertTrue(self.factory.contains_bean("prototypeService"))

        # 销毁单例Bean
        self.factory.destroy_singletons()

        # 验证只有单例Bean被销毁
        self.assertFalse(self.factory.contains_bean("singletonService"))
        self.assertTrue(self.factory.contains_bean("prototypeService"))


class ListableBeanFactoryTestCase(unittest.TestCase):
    """可列举Bean工厂测试"""

    def setUp(self):
        self.factory = TestListableBeanFactory()

        # 注册不同类型的Bean
        test_service1 = TestService("test1")
        test_def1 = BeanDefinition("testService1", TestService, BeanScope.SINGLETON)
        self.factory._register_bean("testService1", test_service1, test_def1)

        test_service2 = TestService("test2")
        test_def2 = BeanDefinition("testService2", TestService, BeanScope.PROTOTYPE)
        self.factory._register_bean("testService2", test_service2, test_def2)

        db_service = DatabaseService()
        db_def = BeanDefinition("dbService", DatabaseService, BeanScope.SINGLETON)
        self.factory._register_bean("dbService", db_service, db_def)

    def test_get_beans_of_type(self):
        """测试按类型获取Bean"""
        test_services = self.factory.get_beans_of_type(TestService)
        self.assertEqual(len(test_services), 2)
        self.assertIn("testService1", test_services)
        self.assertIn("testService2", test_services)

        db_services = self.factory.get_beans_of_type(DatabaseService)
        self.assertEqual(len(db_services), 1)
        self.assertIn("dbService", db_services)

    def test_get_beans_of_type_singletons_only(self):
        """测试只获取单例Bean"""
        test_services = self.factory.get_beans_of_type(TestService, include_non_singletons=False)
        self.assertEqual(len(test_services), 1)
        self.assertIn("testService1", test_services)
        self.assertNotIn("testService2", test_services)

    def test_get_bean_names_for_type(self):
        """测试按类型获取Bean名称"""
        test_names = self.factory.get_bean_names_for_type(TestService)
        self.assertEqual(len(test_names), 2)
        self.assertIn("testService1", test_names)
        self.assertIn("testService2", test_names)

        db_names = self.factory.get_bean_names_for_type(DatabaseService)
        self.assertEqual(len(db_names), 1)
        self.assertIn("dbService", db_names)

    def test_get_bean_names_for_type_singletons_only(self):
        """测试只获取单例Bean名称"""
        test_names = self.factory.get_bean_names_for_type(TestService, include_non_singletons=False)
        self.assertEqual(len(test_names), 1)
        self.assertIn("testService1", test_names)
        self.assertNotIn("testService2", test_names)


class BeanPostProcessorTestCase(unittest.TestCase):
    """Bean后置处理器测试"""

    def setUp(self):
        self.processor = TestBeanPostProcessor()

    def test_pre_process(self):
        """测试前置处理"""
        bean = TestService("test")
        result = self.processor.pre_process(bean, "testBean")

        self.assertIs(result, bean)
        self.assertIn("testBean", self.processor.pre_processed)

    def test_post_process(self):
        """测试后置处理"""
        bean = TestService("test")
        result = self.processor.post_process(bean, "testBean")

        self.assertIs(result, bean)
        self.assertIn("testBean", self.processor.post_processed)

    def test_processing_order(self):
        """测试处理顺序"""
        bean = TestService("test")

        # 先前置处理
        self.processor.pre_process(bean, "testBean")
        # 再后置处理
        self.processor.post_process(bean, "testBean")

        self.assertEqual(len(self.processor.pre_processed), 1)
        self.assertEqual(len(self.processor.post_processed), 1)
        self.assertEqual(self.processor.pre_processed[0], "testBean")
        self.assertEqual(self.processor.post_processed[0], "testBean")


if __name__ == '__main__':
    unittest.main()
