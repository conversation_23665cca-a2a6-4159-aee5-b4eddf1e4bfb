#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
监控集成管理器

负责监控组件的自动集成和生命周期管理，替代硬编码的 Actuator 集成。

核心功能：
- 自动发现和集成监控组件
- 监控组件生命周期管理
- 事件驱动的集成机制
- Web 框架集成支持

设计原则：
- 扫描驱动：通过接口扫描自动发现组件
- 条件化集成：只有当组件存在时才启用
- 优雅降级：集成失败不影响应用启动
- 事件驱动：支持监控事件的发布和监听
"""

import asyncio
from typing import Any, Dict, List, Optional

from loguru import logger

from .discovery import MonitoringComponentDiscovery
from .interfaces import (EndpointInfo, EndpointProvider, HealthIndicator,
                         HealthStatus, MetricsCollector, MonitoringContext,
                         MonitoringEventListener)


class MonitoringIntegration:
    """监控集成管理器

    替代硬编码的 Actuator 集成，实现基于接口的自动监控集成。
    """

    def __init__(self, application_context):
        """初始化监控集成管理器

        Args:
            application_context: 应用上下文实例
        """
        self.application_context = application_context
        self.discovery = MonitoringComponentDiscovery(application_context)

        # 组件存储
        self.monitoring_contexts: List[MonitoringContext] = []
        self.endpoint_providers: List[EndpointProvider] = []
        self.metrics_collectors: List[MetricsCollector] = []
        self.health_indicators: List[HealthIndicator] = []
        self.event_listeners: List[MonitoringEventListener] = []

        # 状态管理
        self._integrated = False
        self._integration_start_time: Optional[float] = None
        self._integration_stats = {
            "contexts_started": 0,
            "endpoints_registered": 0,
            "collectors_activated": 0,
            "indicators_enabled": 0,
            "listeners_registered": 0,
            "integration_errors": 0
        }

    async def auto_integrate_monitoring(self) -> bool:
        """自动集成监控组件

        通过扫描发现监控组件并自动集成，实现零配置的监控启用。

        Returns:
            bool: 集成是否成功
        """
        if self._integrated:
            logger.warning("Monitoring integration already completed")
            return True

        logger.info("🔍 Starting automatic monitoring integration...")
        self._integration_start_time = asyncio.get_event_loop().time()

        try:
            # 1. 发现所有监控组件
            await self._discover_all_components()

            # 2. 检查是否有可集成的组件
            if not self._has_any_components():
                logger.info("No monitoring components found, skipping monitoring integration")
                return False

            # 3. 启动监控上下文
            await self._start_monitoring_contexts()

            # 4. 注册端点提供者
            await self._register_endpoint_providers()

            # 5. 激活指标收集器
            await self._activate_metrics_collectors()

            # 6. 启用健康检查指示器
            await self._enable_health_indicators()

            # 7. 注册事件监听器
            await self._register_event_listeners()

            # 8. 发布集成完成事件
            await self._publish_integration_events()

            self._integrated = True
            integration_time = asyncio.get_event_loop().time() - self._integration_start_time

            logger.info(f"✅ Monitoring integration completed in {integration_time:.3f}s: "
                       f"{self._integration_stats['contexts_started']} contexts, "
                       f"{self._integration_stats['endpoints_registered']} endpoints, "
                       f"{self._integration_stats['collectors_activated']} collectors, "
                       f"{self._integration_stats['indicators_enabled']} indicators")

            return True

        except Exception as e:
            logger.error(f"Failed to integrate monitoring components: {e}")
            self._integration_stats["integration_errors"] += 1
            return False

    async def shutdown_monitoring(self) -> None:
        """关闭监控集成"""
        if not self._integrated:
            return

        logger.info("🛑 Shutting down monitoring integration...")

        try:
            # 1. 发布停止事件
            for listener in self.event_listeners:
                for context in self.monitoring_contexts:
                    try:
                        await listener.on_monitoring_stopped(context)
                    except Exception as e:
                        logger.warning(f"Error notifying listener of monitoring stop: {e}")

            # 2. 停止监控上下文
            for context in self.monitoring_contexts:
                try:
                    await context.stop()
                    logger.debug(f"Stopped monitoring context: {context.__class__.__name__}")
                except Exception as e:
                    logger.warning(f"Failed to stop monitoring context {context.__class__.__name__}: {e}")

            self._integrated = False
            logger.info("Monitoring integration shutdown completed")

        except Exception as e:
            logger.error(f"Error during monitoring shutdown: {e}")

    async def _discover_all_components(self) -> None:
        """发现所有监控组件"""
        logger.debug("Discovering monitoring components...")

        self.monitoring_contexts = self.discovery.discover_monitoring_contexts()
        self.endpoint_providers = self.discovery.discover_endpoint_providers()
        self.metrics_collectors = self.discovery.discover_metrics_collectors()
        self.health_indicators = self.discovery.discover_health_indicators()
        self.event_listeners = self.discovery.discover_event_listeners()

        logger.info(f"Component discovery completed: "
                   f"{len(self.monitoring_contexts)} contexts, "
                   f"{len(self.endpoint_providers)} endpoints, "
                   f"{len(self.metrics_collectors)} collectors, "
                   f"{len(self.health_indicators)} indicators, "
                   f"{len(self.event_listeners)} listeners")

    def _has_any_components(self) -> bool:
        """检查是否有任何监控组件"""
        return (len(self.monitoring_contexts) > 0 or
                len(self.endpoint_providers) > 0 or
                len(self.metrics_collectors) > 0 or
                len(self.health_indicators) > 0)

    async def _start_monitoring_contexts(self) -> None:
        """启动监控上下文"""
        for context in self.monitoring_contexts:
            try:
                success = await context.start()
                if success:
                    self._integration_stats["contexts_started"] += 1
                    logger.info(f"Started monitoring context: {context.__class__.__name__}")

                    # 通知事件监听器
                    for listener in self.event_listeners:
                        try:
                            await listener.on_monitoring_started(context)
                        except Exception as e:
                            logger.warning(f"Error notifying listener of monitoring start: {e}")
                else:
                    logger.warning(f"Failed to start monitoring context: {context.__class__.__name__}")

            except Exception as e:
                logger.warning(f"Error starting monitoring context {context.__class__.__name__}: {e}")
                self._integration_stats["integration_errors"] += 1

    async def _register_endpoint_providers(self) -> None:
        """注册端点提供者"""
        for provider in self.endpoint_providers:
            try:
                if provider.is_enabled():
                    endpoint_info = provider.get_endpoint_info()

                    # 实际注册端点到监控上下文
                    for context in self.monitoring_contexts:
                        try:
                            # 检查 ActuatorContext 是否有 register_endpoint 方法
                            if hasattr(context, 'register_endpoint') and hasattr(provider, 'endpoint_id'):
                                # 如果 provider 本身就是 Endpoint，直接注册
                                if hasattr(provider, 'id'):
                                    success = context.register_endpoint(provider)
                                    if success:
                                        logger.debug(f"Successfully registered endpoint {provider.id} to {context.__class__.__name__}")
                                    # 注意：不再记录失败警告，因为 ActuatorContext 已经处理了重复注册
                                else:
                                    logger.debug(f"Provider {provider.__class__.__name__} is not an Endpoint instance")
                            else:
                                logger.debug(f"Context {context.__class__.__name__} does not support endpoint registration")
                        except Exception as e:
                            logger.debug(f"Error registering endpoint to context {context.__class__.__name__}: {e}")

                    self._integration_stats["endpoints_registered"] += 1
                    logger.info(f"Registered endpoint: {endpoint_info.name} at {endpoint_info.path}")

                    # 通知事件监听器
                    for listener in self.event_listeners:
                        try:
                            await listener.on_endpoint_registered(endpoint_info)
                        except Exception as e:
                            logger.warning(f"Error notifying listener of endpoint registration: {e}")
                else:
                    logger.debug(f"Endpoint provider disabled: {provider.__class__.__name__}")

            except Exception as e:
                logger.warning(f"Error registering endpoint provider {provider.__class__.__name__}: {e}")
                self._integration_stats["integration_errors"] += 1

    async def _activate_metrics_collectors(self) -> None:
        """激活指标收集器"""
        for collector in self.metrics_collectors:
            try:
                if collector.is_available():
                    collector_name = collector.get_collector_name()
                    supported_metrics = collector.get_supported_metrics()
                    self._integration_stats["collectors_activated"] += 1
                    logger.info(f"Activated metrics collector: {collector_name} "
                               f"(supports {len(supported_metrics)} metrics)")
                else:
                    logger.debug(f"Metrics collector unavailable: {collector.__class__.__name__}")

            except Exception as e:
                logger.warning(f"Error activating metrics collector {collector.__class__.__name__}: {e}")
                self._integration_stats["integration_errors"] += 1

    async def _enable_health_indicators(self) -> None:
        """启用健康检查指示器"""
        for indicator in self.health_indicators:
            try:
                indicator_name = indicator.get_name()
                # 执行一次健康检查以验证指示器工作正常
                health_status = await indicator.health()
                self._integration_stats["indicators_enabled"] += 1
                logger.info(f"Enabled health indicator: {indicator_name} (status: {health_status.status})")

            except Exception as e:
                logger.warning(f"Error enabling health indicator {indicator.__class__.__name__}: {e}")
                self._integration_stats["integration_errors"] += 1

    async def _register_event_listeners(self) -> None:
        """注册事件监听器"""
        self._integration_stats["listeners_registered"] = len(self.event_listeners)
        if self.event_listeners:
            logger.info(f"Registered {len(self.event_listeners)} monitoring event listeners")

    async def _publish_integration_events(self) -> None:
        """发布集成完成事件"""
        # 这里可以发布自定义的集成完成事件
        # 例如通知其他系统监控已就绪
        pass

    def get_monitoring_info(self) -> Dict[str, Any]:
        """获取监控集成信息"""
        return {
            "integrated": self._integrated,
            "integration_time": (asyncio.get_event_loop().time() - self._integration_start_time)
                               if self._integration_start_time else None,
            "statistics": self._integration_stats.copy(),
            "components": {
                "monitoring_contexts": len(self.monitoring_contexts),
                "endpoint_providers": len(self.endpoint_providers),
                "metrics_collectors": len(self.metrics_collectors),
                "health_indicators": len(self.health_indicators),
                "event_listeners": len(self.event_listeners)
            },
            "contexts": [ctx.__class__.__name__ for ctx in self.monitoring_contexts],
            "endpoints": [ep.get_endpoint_info().name for ep in self.endpoint_providers if ep.is_enabled()],
            "collectors": [col.get_collector_name() for col in self.metrics_collectors if col.is_available()],
            "indicators": [ind.get_name() for ind in self.health_indicators]
        }

    async def get_health_status(self) -> Dict[str, HealthStatus]:
        """获取所有健康检查状态"""
        health_statuses = {}

        for indicator in self.health_indicators:
            try:
                indicator_name = indicator.get_name()
                health_status = await indicator.health()
                health_statuses[indicator_name] = health_status
            except Exception as e:
                logger.warning(f"Error getting health status from {indicator.__class__.__name__}: {e}")
                health_statuses[indicator.get_name()] = HealthStatus(
                    status="UNKNOWN",
                    description=f"Health check failed: {str(e)}"
                )

        return health_statuses

    def get_metrics_data(self) -> Dict[str, Any]:
        """获取所有指标数据"""
        all_metrics = {}

        for collector in self.metrics_collectors:
            try:
                if collector.is_available():
                    collector_name = collector.get_collector_name()
                    metrics = collector.collect_metrics()
                    all_metrics[collector_name] = [
                        {
                            "name": metric.name,
                            "value": metric.value,
                            "unit": metric.unit,
                            "tags": metric.tags,
                            "timestamp": metric.timestamp
                        }
                        for metric in metrics
                    ]
            except Exception as e:
                logger.warning(f"Error collecting metrics from {collector.__class__.__name__}: {e}")

        return all_metrics
