#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean注册表模块单元测试
"""

import unittest
import threading
import time

from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.bean.definition import BeanDefinition


class TestService:
    """测试用的服务类"""
    def __init__(self, name: str = "test"):
        self.name = name


class TestRepository:
    """测试用的仓库类"""
    def __init__(self):
        self.data = []


class DefaultBeanDefinitionRegistryTestCase(unittest.TestCase):
    """DefaultBeanDefinitionRegistry测试"""

    def setUp(self):
        """测试前准备"""
        self.registry = DefaultBeanDefinitionRegistry()

    def tearDown(self):
        """测试后清理"""
        self.registry = None

    def test_registry_initialization(self):
        """测试注册表初始化"""
        registry = DefaultBeanDefinitionRegistry()
        self.assertIsNotNone(registry._bean_definitions)
        self.assertEqual(len(list(registry.names())), 0)

    def test_register_bean_definition_success(self):
        """测试注册Bean定义成功"""
        definition = BeanDefinition("testBean", TestService)

        self.registry.register("testBean", definition)

        self.assertTrue(self.registry.has_definition("testBean"))
        retrieved = self.registry.get_definition("testBean")
        self.assertIs(retrieved, definition)

    def test_register_bean_definition_duplicate_name(self):
        """测试注册重复名称的Bean定义"""
        definition1 = BeanDefinition("testBean", TestService)
        definition2 = BeanDefinition("testBean", TestRepository)

        # 第一次注册
        self.registry.register("testBean", definition1)

        # 第二次注册应该覆盖
        self.registry.register("testBean", definition2)

        retrieved = self.registry.get_definition("testBean")
        self.assertIs(retrieved, definition2)

    def test_register_bean_definition_validation_error(self):
        """测试注册Bean定义验证错误"""
        # 创建无效的Bean定义
        with self.assertRaises(ValueError):
            BeanDefinition("", TestService)  # 空名称

    def test_has_definition_true(self):
        """测试Bean定义存在检查 - 存在"""
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        self.assertTrue(self.registry.has_definition("testBean"))

    def test_has_definition_false(self):
        """测试Bean定义存在检查 - 不存在"""
        self.assertFalse(self.registry.has_definition("nonExistentBean"))

    def test_get_definition_success(self):
        """测试获取Bean定义成功"""
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        retrieved = self.registry.get_definition("testBean")
        self.assertIs(retrieved, definition)

    def test_get_definition_not_found(self):
        """测试获取不存在的Bean定义"""
        with self.assertRaises(KeyError) as context:
            self.registry.get_definition("nonExistentBean")
        self.assertIn("No bean definition found", str(context.exception))

    def test_remove_definition_success(self):
        """测试移除Bean定义成功"""
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 确认存在
        self.assertTrue(self.registry.has_definition("testBean"))

        # 移除
        self.registry.remove("testBean")

        # 确认已移除
        self.assertFalse(self.registry.has_definition("testBean"))

    def test_remove_definition_not_found(self):
        """测试移除不存在的Bean定义"""
        with self.assertRaises(KeyError) as context:
            self.registry.remove("nonExistentBean")
        self.assertIn("No bean definition found", str(context.exception))

    def test_get_bean_names(self):
        """测试获取所有Bean名称"""
        # 注册几个Bean
        self.registry.register("bean1", BeanDefinition("bean1", TestService))
        self.registry.register("bean2", BeanDefinition("bean2", TestRepository))

        names = list(self.registry.names())
        self.assertEqual(len(names), 2)
        self.assertIn("bean1", names)
        self.assertIn("bean2", names)

    def test_get_bean_names_empty(self):
        """测试获取Bean名称 - 空注册表"""
        names = list(self.registry.names())
        self.assertEqual(len(names), 0)

    def test_get_bean_count(self):
        """测试获取Bean数量"""
        # 初始为0
        self.assertEqual(self.registry.count(), 0)

        # 注册一些Bean
        self.registry.register("bean1", BeanDefinition("bean1", TestService))
        self.assertEqual(self.registry.count(), 1)

        self.registry.register("bean2", BeanDefinition("bean2", TestRepository))
        self.assertEqual(self.registry.count(), 2)

        # 移除一个Bean
        self.registry.remove("bean1")
        self.assertEqual(self.registry.count(), 1)

    def test_find_by_type_exact_match(self):
        """测试按类型查找Bean - 精确匹配"""
        service_def = BeanDefinition("service", TestService)
        repo_def = BeanDefinition("repo", TestRepository)

        self.registry.register("service", service_def)
        self.registry.register("repo", repo_def)

        # 查找TestService类型
        service_names = self.registry.find_by_type(TestService)
        self.assertEqual(len(service_names), 1)
        self.assertIn("service", service_names)

        # 查找TestRepository类型
        repo_names = self.registry.find_by_type(TestRepository)
        self.assertEqual(len(repo_names), 1)
        self.assertIn("repo", repo_names)

    def test_find_by_type_no_match(self):
        """测试按类型查找Bean - 无匹配"""
        self.registry.register("service", BeanDefinition("service", TestService))

        # 查找不存在的类型
        names = self.registry.find_by_type(str)
        self.assertEqual(len(names), 0)

    def test_find_by_type_multiple_matches(self):
        """测试按类型查找Bean - 多个匹配"""
        service1_def = BeanDefinition("service1", TestService)
        service2_def = BeanDefinition("service2", TestService)

        self.registry.register("service1", service1_def)
        self.registry.register("service2", service2_def)

        names = self.registry.find_by_type(TestService)
        self.assertEqual(len(names), 2)
        self.assertIn("service1", names)
        self.assertIn("service2", names)

    # 注意：以下方法在当前实现中不存在，已移除相关测试
    # - find_primary()
    # - get_definitions_by_scope()
    # 这些功能可能在未来版本中实现

    def test_thread_safety(self):
        """测试线程安全性"""
        results = []
        errors = []

        def register_beans(start_index):
            try:
                for i in range(start_index, start_index + 10):
                    bean_name = f"bean_{i}"
                    definition = BeanDefinition(bean_name, TestService)
                    self.registry.register(bean_name, definition)
                    results.append(bean_name)
            except Exception as e:
                errors.append(e)

        # 创建多个线程同时注册Bean
        threads = [
            threading.Thread(target=register_beans, args=(i * 10,))
            for i in range(5)
        ]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # 检查结果
        self.assertEqual(len(errors), 0)  # 不应该有错误
        self.assertEqual(len(results), 50)  # 应该注册50个Bean
        self.assertEqual(self.registry.count(), 50)  # 注册表应该有50个Bean

    def test_concurrent_read_write(self):
        """测试并发读写"""
        # 先注册一些Bean
        for i in range(10):
            bean_name = f"bean_{i}"
            definition = BeanDefinition(bean_name, TestService)
            self.registry.register(bean_name, definition)

        read_results = []
        write_results = []

        def read_beans():
            for i in range(10):
                bean_name = f"bean_{i}"
                if self.registry.has_definition(bean_name):
                    definition = self.registry.get_definition(bean_name)
                    read_results.append(definition.bean_name)

        def write_beans():
            for i in range(10, 20):
                bean_name = f"bean_{i}"
                definition = BeanDefinition(bean_name, TestService)
                self.registry.register(bean_name, definition)
                write_results.append(bean_name)

        # 创建读写线程
        read_thread = threading.Thread(target=read_beans)
        write_thread = threading.Thread(target=write_beans)

        read_thread.start()
        write_thread.start()

        read_thread.join()
        write_thread.join()

        # 检查结果
        self.assertEqual(len(read_results), 10)
        self.assertEqual(len(write_results), 10)
        self.assertEqual(self.registry.count(), 20)


# ==================== DefaultBeanDefinitionRegistry高级功能测试 ====================

class DefaultBeanDefinitionRegistryAdvancedTestCase(unittest.TestCase):
    """DefaultBeanDefinitionRegistry高级功能测试"""

    def setUp(self):
        """测试前准备"""
        self.registry = DefaultBeanDefinitionRegistry()

    def tearDown(self):
        """测试后清理"""
        pass

    def test_thread_safety_disabled(self):
        """测试禁用线程安全"""
        registry = DefaultBeanDefinitionRegistry(enable_thread_safety=False)
        self.assertIsNone(registry._lock)

        # 基本操作应该仍然工作
        definition = BeanDefinition("testBean", TestService)
        registry.register("testBean", definition)

        self.assertTrue(registry.has_definition("testBean"))
        retrieved = registry.get_definition("testBean")
        self.assertIs(retrieved, definition)

    def test_find_by_type(self):
        """测试按类型查找Bean定义"""
        # 注册不同类型的Bean
        service_def = BeanDefinition("testService", TestService)
        repo_def = BeanDefinition("testRepo", TestRepository)
        service2_def = BeanDefinition("testService2", TestService)

        self.registry.register("testService", service_def)
        self.registry.register("testRepo", repo_def)
        self.registry.register("testService2", service2_def)

        # 查找TestService类型的Bean
        service_beans = self.registry.find_by_type(TestService)

        self.assertEqual(len(service_beans), 2)
        self.assertIn("testService", service_beans)
        self.assertIn("testService2", service_beans)
        self.assertNotIn("testRepo", service_beans)

        # 查找TestRepository类型的Bean
        repo_beans = self.registry.find_by_type(TestRepository)

        self.assertEqual(len(repo_beans), 1)
        self.assertIn("testRepo", repo_beans)

    def test_find_by_type_empty(self):
        """测试按不存在的类型查找"""
        # 注册一些Bean
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 查找不存在的类型
        class NonExistentClass:
            pass

        result = self.registry.find_by_type(NonExistentClass)
        self.assertEqual(len(result), 0)

        # 查找None类型（跳过，因为API不支持）
        # result = self.registry.find_by_type(None)
        # self.assertEqual(len(result), 0)

    def test_find_primary_by_type(self):
        """测试按类型查找主要Bean定义"""
        # 创建主要和非主要Bean定义
        primary_def = BeanDefinition("primaryService", TestService, primary=True)
        secondary_def = BeanDefinition("secondaryService", TestService, primary=False)

        self.registry.register("primaryService", primary_def)
        self.registry.register("secondaryService", secondary_def)

        # 查找主要Bean（使用实际存在的方法）
        all_beans = self.registry.find_by_type(TestService)
        primary_beans = {name: definition for name, definition in all_beans.items() if definition.is_primary()}

        self.assertEqual(len(primary_beans), 1)
        self.assertIn("primaryService", primary_beans)
        self.assertNotIn("secondaryService", primary_beans)

    def test_singleton_names(self):
        """测试获取单例Bean名称"""
        from miniboot.bean.definition import BeanScope

        # 注册不同作用域的Bean
        singleton_def = BeanDefinition("singletonBean", TestService, BeanScope.SINGLETON)
        prototype_def = BeanDefinition("prototypeBean", TestService, BeanScope.PROTOTYPE)

        self.registry.register("singletonBean", singleton_def)
        self.registry.register("prototypeBean", prototype_def)

        # 获取单例Bean名称
        singleton_names = self.registry.singleton_names()

        self.assertIn("singletonBean", singleton_names)
        self.assertNotIn("prototypeBean", singleton_names)

    def test_get_by_names_batch(self):
        """测试批量获取Bean定义"""
        # 注册多个Bean
        def1 = BeanDefinition("bean1", TestService)
        def2 = BeanDefinition("bean2", TestRepository)
        def3 = BeanDefinition("bean3", TestService)

        self.registry.register("bean1", def1)
        self.registry.register("bean2", def2)
        self.registry.register("bean3", def3)

        # 批量获取
        bean_names = ["bean1", "bean3"]
        result = self.registry.get_by_names(bean_names)

        self.assertEqual(len(result), 2)
        self.assertIn("bean1", result)
        self.assertIn("bean3", result)
        self.assertNotIn("bean2", result)
        self.assertIs(result["bean1"], def1)
        self.assertIs(result["bean3"], def3)

    def test_get_by_names_empty_list(self):
        """测试批量获取空列表"""
        result = self.registry.get_by_names([])
        self.assertEqual(len(result), 0)

    def test_get_by_names_not_found(self):
        """测试批量获取不存在的Bean"""
        # 注册一个Bean
        definition = BeanDefinition("existingBean", TestService)
        self.registry.register("existingBean", definition)

        # 尝试获取包含不存在Bean的列表
        with self.assertRaises(KeyError):
            self.registry.get_by_names(["existingBean", "nonExistentBean"])

    def test_clear_registry(self):
        """测试清空注册表"""
        # 注册一些Bean
        def1 = BeanDefinition("bean1", TestService)
        def2 = BeanDefinition("bean2", TestRepository)

        self.registry.register("bean1", def1)
        self.registry.register("bean2", def2)

        # 验证Bean存在
        self.assertEqual(self.registry.count(), 2)
        self.assertTrue(self.registry.has_definition("bean1"))
        self.assertTrue(self.registry.has_definition("bean2"))

        # 清空注册表
        self.registry.clear()

        # 验证已清空
        self.assertEqual(self.registry.count(), 0)
        self.assertFalse(self.registry.has_definition("bean1"))
        self.assertFalse(self.registry.has_definition("bean2"))

    def test_registration_and_removal_tracking(self):
        """测试注册和移除跟踪"""
        # 初始状态
        initial_count = self.registry.count()

        # 注册Bean
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 检查计数增加
        self.assertEqual(self.registry.count(), initial_count + 1)
        self.assertTrue(self.registry.has_definition("testBean"))

        # 移除Bean
        self.registry.remove("testBean")

        # 检查计数减少
        self.assertEqual(self.registry.count(), initial_count)
        self.assertFalse(self.registry.has_definition("testBean"))

    def test_magic_methods(self):
        """测试魔术方法"""
        # 注册Bean
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 测试 __contains__ (使用has_definition方法)
        self.assertTrue(self.registry.has_definition("testBean"))
        self.assertFalse(self.registry.has_definition("nonExistentBean"))

        # 测试 __iter__
        bean_names = list(self.registry)
        self.assertIn("testBean", bean_names)

        # 测试 __str__
        str_repr = str(self.registry)
        self.assertIn("DefaultBeanDefinitionRegistry", str_repr)
        self.assertIn("count=1", str_repr)

        # 测试 __repr__
        repr_str = repr(self.registry)
        self.assertIn("DefaultBeanDefinitionRegistry", repr_str)
        self.assertIn("testBean", repr_str)

    def test_compatibility_methods(self):
        """测试兼容性方法"""
        # 注册Bean
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 测试兼容性方法
        self.assertEqual(self.registry.get_bean_names(), self.registry.names())
        self.assertEqual(self.registry.get_bean_definition_names(), self.registry.names())
        self.assertEqual(self.registry.get_bean_definition_count(), self.registry.count())

    def test_concurrent_access(self):
        """测试并发访问"""
        import threading

        results = []
        errors = []

        def register_beans(thread_id):
            try:
                for i in range(10):
                    bean_name = f"bean_{thread_id}_{i}"
                    definition = BeanDefinition(bean_name, TestService)
                    self.registry.register(bean_name, definition)
                    results.append(bean_name)
                    time.sleep(0.001)  # 模拟处理时间
            except Exception as e:
                errors.append((thread_id, e))

        def read_beans(thread_id):
            try:
                for i in range(5):
                    _ = self.registry.names()  # 触发读取操作
                    count = self.registry.count()
                    results.append(f"read_{thread_id}_{i}_{count}")
                    time.sleep(0.001)
            except Exception as e:
                errors.append((thread_id, e))

        # 创建多个线程
        threads = []

        # 写线程
        for i in range(3):
            thread = threading.Thread(target=register_beans, args=(i,))
            threads.append(thread)

        # 读线程
        for i in range(2):
            thread = threading.Thread(target=read_beans, args=(i,))
            threads.append(thread)

        # 启动所有线程
        for thread in threads:
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证结果
        self.assertEqual(len(errors), 0)  # 不应该有错误
        self.assertGreater(len(results), 0)  # 应该有结果

        # 验证最终状态
        final_count = self.registry.count()
        self.assertGreater(final_count, 0)


if __name__ == '__main__':
    unittest.main()
